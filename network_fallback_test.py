"""
Fallback test version of the GPU-accelerated BipartiteNetwork.
This version falls back to NumPy if CuPy is not available, for testing purposes.
"""

# Try to import CuPy, fall back to NumPy if not available
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("✓ CuPy available - using GPU acceleration")
except ImportError:
    import numpy as cp  # Use NumPy as fallback
    GPU_AVAILABLE = False
    print("⚠ CuPy not available - falling back to NumPy (CPU)")

import numpy as np


class BipartiteNetwork:
    """
    Bipartite neural network with spike-timing dependent plasticity.
    GPU-accelerated when CuPy is available, falls back to CPU otherwise.
    """
    
    def __init__(self, n_A=50, n_B=50, connection_prob=0.3):
        """Initialize the bipartite network with specified parameters."""
        self.n_A = n_A
        self.n_B = n_B
        self.connection_prob = connection_prob
        
        # Lognormal distribution parameters
        self.lognorm_mu_BA = 0.5
        self.lognorm_sigma_BA = 0.5
        self.lognorm_scale_BA = 0.25
        self.lognorm_mu_AB = 0.5
        self.lognorm_sigma_AB = 0.5
        self.lognorm_scale_AB = 0.25
        
        # LIF parameters
        self.V_rest = -70.0
        self.V_threshold = -50.0
        self.V_reset = -80.0
        self.tau_m = 10.0
        self.R_m = 10.0
        self.refractory_period = 2.0
        
        # Initialize arrays on GPU/CPU
        self.V_m_A = cp.full(n_A, self.V_rest, dtype=cp.float64)
        self.V_m_B = cp.full(n_B, self.V_rest, dtype=cp.float64)
        self.last_spike_time_A = cp.full(n_A, -1000.0, dtype=cp.float64)
        self.last_spike_time_B = cp.full(n_B, -1000.0, dtype=cp.float64)
        self.refractory_until_A = cp.full(n_A, -1000.0, dtype=cp.float64)
        self.refractory_until_B = cp.full(n_B, -1000.0, dtype=cp.float64)
        
        # Spike history
        self.max_spike_history = 100
        self.spike_history_A = cp.full((n_A, self.max_spike_history), -1000.0, dtype=cp.float64)
        self.spike_history_B = cp.full((n_B, self.max_spike_history), -1000.0, dtype=cp.float64)
        self.spike_history_idx_A = cp.zeros(n_A, dtype=cp.int32)
        self.spike_history_idx_B = cp.zeros(n_B, dtype=cp.int32)
        
        # STDP parameters
        self.A_LTP = 0.01
        self.A_LTD = 0.0105
        self.tau_LTP = 20.0
        self.tau_LTD = 20.0
        self.synaptic_strength = 15.0
        
        # Initialize connections
        self.initialize_connections()
        
        # Simulation parameters
        self.dt = 1.0
        self.current_time = 0.0
        self.background_rate_A = 5.0
        self.background_rate_B = 5.0
        
        # Pre-allocate spike arrays
        self.spikes_A_arr = cp.zeros(self.n_A, dtype=bool)
        self.spikes_B_arr = cp.zeros(self.n_B, dtype=bool)
    
    def initialize_connections(self):
        """Initialize sparse random connectivity between layers."""
        # W_BA: excitatory connections from B to A
        rand_matrix_BA = cp.random.rand(self.n_A, self.n_B)
        lognorm_weights_BA = cp.random.lognormal(
            mean=self.lognorm_mu_BA,
            sigma=self.lognorm_sigma_BA,
            size=(self.n_A, self.n_B)
        ) * self.lognorm_scale_BA
        self.W_BA = cp.where(
            rand_matrix_BA < self.connection_prob,
            lognorm_weights_BA,
            0
        )
        
        # W_AB: inhibitory connections from A to B
        rand_matrix_AB = cp.random.rand(self.n_B, self.n_A)
        lognorm_weights_AB = cp.random.lognormal(
            mean=self.lognorm_mu_AB,
            sigma=self.lognorm_sigma_AB,
            size=(self.n_B, self.n_A)
        ) * self.lognorm_scale_AB
        self.W_AB = cp.where(
            rand_matrix_AB < self.connection_prob,
            -lognorm_weights_AB,
            0
        )
    
    def poisson_input(self, rate_hz, dt, n_neurons):
        """Generate Poisson spike train for a population of neurons."""
        prob = rate_hz * (dt / 1000.0)
        return cp.random.random(n_neurons) < prob
    
    def update(self):
        """Update network for one time step."""
        self.current_time += self.dt
        
        # Calculate synaptic inputs
        I_A = cp.zeros(self.n_A)
        I_B = cp.zeros(self.n_B)
        
        # Add background Poisson input
        I_A += self.poisson_input(self.background_rate_A, self.dt, self.n_A) * self.synaptic_strength * 2.0
        I_B += self.poisson_input(self.background_rate_B, self.dt, self.n_B) * self.synaptic_strength * 2.0
        
        # Add recurrent inputs
        I_A += (self.W_BA @ self.spikes_B_arr) * self.synaptic_strength
        I_B += (self.W_AB @ self.spikes_A_arr) * self.synaptic_strength
        
        # Reset spike arrays
        self.spikes_A_arr.fill(False)
        self.spikes_B_arr.fill(False)
        
        # Simple LIF update (simplified for testing)
        # Update membrane potentials
        self.V_m_A += ((self.V_rest - self.V_m_A + self.R_m * I_A) / self.tau_m) * self.dt
        self.V_m_B += ((self.V_rest - self.V_m_B + self.R_m * I_B) / self.tau_m) * self.dt
        
        # Detect spikes
        spiked_A = self.V_m_A >= self.V_threshold
        spiked_B = self.V_m_B >= self.V_threshold
        
        spiked_A_indices = cp.where(spiked_A)[0]
        spiked_B_indices = cp.where(spiked_B)[0]
        
        # Reset spiking neurons
        if len(spiked_A_indices) > 0:
            self.V_m_A[spiked_A_indices] = self.V_reset
            self.spikes_A_arr[spiked_A_indices] = True
        
        if len(spiked_B_indices) > 0:
            self.V_m_B[spiked_B_indices] = self.V_reset
            self.spikes_B_arr[spiked_B_indices] = True
        
        # Transfer to CPU for API compatibility
        if GPU_AVAILABLE:
            spiked_A_cpu = spiked_A_indices.get().tolist() if len(spiked_A_indices) > 0 else []
            spiked_B_cpu = spiked_B_indices.get().tolist() if len(spiked_B_indices) > 0 else []
        else:
            spiked_A_cpu = spiked_A_indices.tolist() if len(spiked_A_indices) > 0 else []
            spiked_B_cpu = spiked_B_indices.tolist() if len(spiked_B_indices) > 0 else []
        
        return spiked_A_cpu, spiked_B_cpu
    
    def get_neuron_voltage(self, layer, neuron_idx):
        """Get membrane voltage for a specific neuron."""
        if layer == 'A':
            voltage = self.V_m_A[neuron_idx]
        else:
            voltage = self.V_m_B[neuron_idx]
        
        if GPU_AVAILABLE:
            return float(voltage.get())
        else:
            return float(voltage)
    
    def get_layer_voltages(self, layer):
        """Get all membrane voltages for a layer."""
        if layer == 'A':
            voltages = self.V_m_A
        else:
            voltages = self.V_m_B
        
        if GPU_AVAILABLE:
            return voltages.get().copy()
        else:
            return voltages.copy()


# Test the fallback implementation
if __name__ == "__main__":
    print(f"Testing BipartiteNetwork ({'GPU' if GPU_AVAILABLE else 'CPU'} mode)")
    
    # Create network
    network = BipartiteNetwork(n_A=50, n_B=50)
    print(f"✓ Network created with {network.n_A} A neurons and {network.n_B} B neurons")
    
    # Test simulation
    import time
    start_time = time.time()
    
    for step in range(100):
        spikes_A, spikes_B = network.update()
        if step % 20 == 0:
            print(f"  Step {step}: {len(spikes_A)} A spikes, {len(spikes_B)} B spikes")
    
    elapsed = time.time() - start_time
    print(f"✓ 100 steps completed in {elapsed:.3f} seconds")
    print(f"✓ Rate: {100/elapsed:.1f} steps/second")
    
    # Test API methods
    voltage = network.get_neuron_voltage('A', 0)
    voltages = network.get_layer_voltages('A')
    print(f"✓ API test: voltage={voltage:.2f}mV, layer shape={voltages.shape}")
    
    print("✅ All tests passed!")
