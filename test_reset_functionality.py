#!/usr/bin/env python3
"""
Test script to verify the reset functionality works correctly.

This script tests that the reset_network_state() method properly resets
all neuron states and allows the simulation to continue generating spikes
after reset.

Author: AI Assistant  
Date: 2025-08-05
"""

import numpy as np
import sys
import os

# Add the current directory to the path to import network module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from network import BipartiteNetwork


def test_reset_functionality():
    """
    Test that reset properly restores network to initial state and allows continued spiking.
    """
    print("=" * 60)
    print("TEST: Reset Functionality")
    print("=" * 60)
    
    # Create network
    network = BipartiteNetwork(n_A=10, n_B=10, connection_prob=0.5, use_gpu=False)
    
    print("✓ Network created")
    
    # Store initial states
    initial_V_m_A = network.V_m_A.copy()
    initial_V_m_B = network.V_m_B.copy()
    initial_time = network.current_time
    
    print(f"Initial time: {initial_time}")
    print(f"Initial V_m_A mean: {np.mean(initial_V_m_A):.2f} mV")
    print(f"Initial V_m_B mean: {np.mean(initial_V_m_B):.2f} mV")
    
    # Run simulation for some time to change states
    print("\nRunning simulation for 100 steps...")
    spike_count_before_reset = 0
    
    for step in range(100):
        spikes_A, spikes_B = network.update()
        spike_count_before_reset += len(spikes_A) + len(spikes_B)
    
    # Check that states have changed
    print(f"Time after simulation: {network.current_time}")
    print(f"V_m_A mean after simulation: {np.mean(network.V_m_A):.2f} mV")
    print(f"V_m_B mean after simulation: {np.mean(network.V_m_B):.2f} mV")
    print(f"Total spikes before reset: {spike_count_before_reset}")
    
    # Verify states have changed
    time_changed = network.current_time != initial_time
    voltage_changed = not np.allclose(network.V_m_A, initial_V_m_A) or not np.allclose(network.V_m_B, initial_V_m_B)
    
    print(f"✓ Time changed: {time_changed}")
    print(f"✓ Voltages changed: {voltage_changed}")
    
    if not time_changed or not voltage_changed:
        print("❌ Warning: Network states didn't change during simulation")
    
    # Reset the network
    print("\nResetting network...")
    network.reset_network_state()
    
    # Verify reset worked
    time_reset = network.current_time == 0.0
    voltages_reset_A = np.allclose(network.V_m_A, network.V_rest)
    voltages_reset_B = np.allclose(network.V_m_B, network.V_rest)
    spike_times_reset_A = np.all(network.last_spike_time_A == -1000.0)
    spike_times_reset_B = np.all(network.last_spike_time_B == -1000.0)
    refractory_reset_A = np.all(network.refractory_until_A == -1000.0)
    refractory_reset_B = np.all(network.refractory_until_B == -1000.0)
    
    print(f"✓ Time reset to 0: {time_reset}")
    print(f"✓ V_m_A reset to rest ({network.V_rest} mV): {voltages_reset_A}")
    print(f"✓ V_m_B reset to rest ({network.V_rest} mV): {voltages_reset_B}")
    print(f"✓ Last spike times A reset: {spike_times_reset_A}")
    print(f"✓ Last spike times B reset: {spike_times_reset_B}")
    print(f"✓ Refractory periods A reset: {refractory_reset_A}")
    print(f"✓ Refractory periods B reset: {refractory_reset_B}")
    
    # Test that simulation can continue generating spikes after reset
    print("\nTesting spike generation after reset...")
    spike_count_after_reset = 0
    
    for step in range(100):
        spikes_A, spikes_B = network.update()
        spike_count_after_reset += len(spikes_A) + len(spikes_B)
    
    print(f"Total spikes after reset: {spike_count_after_reset}")
    print(f"Time after post-reset simulation: {network.current_time}")
    
    # Check if spikes are being generated
    spikes_generated = spike_count_after_reset > 0
    time_advancing = network.current_time > 0
    
    print(f"✓ Spikes generated after reset: {spikes_generated}")
    print(f"✓ Time advancing after reset: {time_advancing}")
    
    # Overall test result
    reset_successful = (time_reset and voltages_reset_A and voltages_reset_B and 
                       spike_times_reset_A and spike_times_reset_B and
                       refractory_reset_A and refractory_reset_B)
    
    simulation_working = spikes_generated and time_advancing
    
    print("\n" + "=" * 60)
    print("RESULTS")
    print("=" * 60)
    print(f"Reset functionality: {'PASSED' if reset_successful else 'FAILED'}")
    print(f"Post-reset simulation: {'PASSED' if simulation_working else 'FAILED'}")
    
    if reset_successful and simulation_working:
        print("\n🎉 RESET TEST PASSED! Reset button should work correctly.")
        return True
    else:
        print("\n❌ RESET TEST FAILED! Reset functionality has issues.")
        return False


def test_reset_button_simulation():
    """
    Test that simulates the actual reset button behavior (including connection regeneration).
    """
    print("\n" + "=" * 60)
    print("TEST: Reset Button Simulation")
    print("=" * 60)

    # Create network
    network = BipartiteNetwork(n_A=5, n_B=5, connection_prob=0.6, use_gpu=False)

    print(f"Initial BA connections: {np.sum(network.connection_mask_BA)}")
    print(f"Initial AB connections: {np.sum(network.connection_mask_AB)}")

    # Run simulation to change neuron states and weights
    print("Running simulation to change states...")
    spike_count_before = 0
    for step in range(50):
        spikes_A, spikes_B = network.update()
        spike_count_before += len(spikes_A) + len(spikes_B)

    print(f"Spikes before reset: {spike_count_before}")
    print(f"Time before reset: {network.current_time}")

    # Store states before reset
    time_before = network.current_time
    V_m_A_before = network.V_m_A.copy()

    # Simulate reset button behavior (what the UI does)
    print("Simulating reset button press...")
    # 1. Regenerate connections (this is what the reset button does)
    network.initialize_connections()
    # 2. Reset neuron states
    network.reset_network_state()

    # Verify reset worked
    time_reset = network.current_time == 0.0
    voltages_reset = np.allclose(network.V_m_A, network.V_rest)
    states_changed = time_before != network.current_time

    print(f"✓ Time reset to 0: {time_reset}")
    print(f"✓ Voltages reset to rest: {voltages_reset}")
    print(f"✓ States changed from before: {states_changed}")

    # Test continued simulation after reset
    print("Testing simulation after reset...")
    spike_count_after = 0
    for step in range(50):
        spikes_A, spikes_B = network.update()
        spike_count_after += len(spikes_A) + len(spikes_B)

    print(f"Spikes after reset: {spike_count_after}")
    print(f"Time after reset simulation: {network.current_time}")

    # Check if simulation is working
    spikes_generated = spike_count_after > 0
    time_advancing = network.current_time > 0

    print(f"✓ Spikes generated after reset: {spikes_generated}")
    print(f"✓ Time advancing after reset: {time_advancing}")

    reset_successful = time_reset and voltages_reset and states_changed
    simulation_working = spikes_generated and time_advancing

    if reset_successful and simulation_working:
        print("✅ RESET BUTTON TEST PASSED! Reset button behavior works correctly.")
        return True
    else:
        print("❌ RESET BUTTON TEST FAILED! Reset button behavior has issues.")
        return False


def main():
    """Run all reset functionality tests."""
    print("Testing Reset Functionality")
    print("===========================")
    
    try:
        test1_passed = test_reset_functionality()
        test2_passed = test_reset_button_simulation()

        print("\n" + "=" * 60)
        print("SUMMARY")
        print("=" * 60)
        print(f"Reset Functionality: {'PASSED' if test1_passed else 'FAILED'}")
        print(f"Reset Button Simulation: {'PASSED' if test2_passed else 'FAILED'}")

        if test1_passed and test2_passed:
            print("\n🎉 ALL TESTS PASSED! Reset button should work correctly.")
            return True
        else:
            print("\n❌ Some tests failed. Please review the implementation.")
            return False
            
    except Exception as e:
        print(f"\n💥 Test execution failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
